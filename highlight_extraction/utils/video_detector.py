#!/usr/bin/env python3
"""
Advanced Multi-Feature Video Highlights Detection System

Comprehensive highlight detection algorithm using sophisticated multi-modal analysis
to identify the most engaging, valuable, and shareable moments in video content.

Core Features:
- spaCy-based sentence segmentation with timestamp realignment
- Multi-label emotion analysis using GoEmotions model
- RoBERTa sentiment analysis with VADER fallback
- SBERT semantic embeddings for novelty detection
- Burstiness metrics for engagement analysis
- Change-point detection for optimal segmentation
- Max-Marginal-Relevance for diverse highlight selection

Algorithm Pipeline:
1. Text Processing: spaCy sentence segmentation with timestamp realignment
2. Feature Extraction: Emotion, sentiment, embeddings, novelty, burstiness
3. Score Fusion: Weighted linear combination of all features
4. Segmentation: Change-point detection with adaptive windowing
5. Selection: Max-Marginal-Relevance algorithm for optimal highlights
"""

import time
import logging
import hashlib
from typing import List, Dict, Any, Optional
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

# Import validation utilities for consistent minimum duration enforcement
try:
    from utils.validation_utils import HighlightValidationUtils
    from config.settings import MIN_HIGHLIGHT_DURATION_SECONDS
except ImportError:
    # Fallback if imports fail
    HighlightValidationUtils = None
    MIN_HIGHLIGHT_DURATION_SECONDS = 10.0

# Check for library availability without importing heavy dependencies
SPACY_AVAILABLE = False
VADER_AVAILABLE = False
RUPTURES_AVAILABLE = False
CHANGEFOREST_AVAILABLE = False
TRANSFORMERS_AVAILABLE = False
SENTENCE_TRANSFORMERS_AVAILABLE = False
SKLEARN_AVAILABLE = False
NUMPY_AVAILABLE = False

try:
    import importlib.util

    # Check spaCy availability
    if importlib.util.find_spec("spacy") is not None:
        SPACY_AVAILABLE = True

    # Check VADER availability
    if importlib.util.find_spec("vaderSentiment") is not None:
        VADER_AVAILABLE = True

    # Check ruptures availability
    if importlib.util.find_spec("ruptures") is not None:
        RUPTURES_AVAILABLE = True

    # Check changeforest availability
    if importlib.util.find_spec("changeforest") is not None:
        CHANGEFOREST_AVAILABLE = True

    # Check transformers availability
    if importlib.util.find_spec("transformers") is not None:
        TRANSFORMERS_AVAILABLE = True

    # Check sentence-transformers availability
    if importlib.util.find_spec("sentence_transformers") is not None:
        SENTENCE_TRANSFORMERS_AVAILABLE = True

    # Check sklearn availability
    if importlib.util.find_spec("sklearn") is not None:
        SKLEARN_AVAILABLE = True

    # Check numpy availability
    if importlib.util.find_spec("numpy") is not None:
        NUMPY_AVAILABLE = True
        import numpy as np

except Exception as e:
    logger.warning(f"Error checking library availability: {e}")

# Log availability status
logger.info(f"Library availability - spaCy: {SPACY_AVAILABLE}, VADER: {VADER_AVAILABLE}, "
           f"ruptures: {RUPTURES_AVAILABLE}, changeforest: {CHANGEFOREST_AVAILABLE}")
logger.info(f"ML Libraries - Transformers: {TRANSFORMERS_AVAILABLE}, "
           f"SentenceTransformers: {SENTENCE_TRANSFORMERS_AVAILABLE}, sklearn: {SKLEARN_AVAILABLE}")

# JSON Schema for highlight validation
HIGHLIGHT_SCHEMA = {
    "type": "object",
    "required": ["start_time", "end_time", "duration", "engagement_score"],
    "properties": {
        "start_time": {"type": "number", "minimum": 0},
        "end_time": {"type": "number", "minimum": 0},
        "duration": {"type": "number", "minimum": 10, "maximum": 30},
        "engagement_score": {"type": "number", "minimum": 0, "maximum": 1},
        "text": {"type": "string"},
        "metadata": {
            "type": "object",
            "properties": {
                "feature_scores": {"type": "object"},
                "peak_engagement_index": {"type": "number"},
                "confidence_score": {"type": "number"},
                "segment_boundaries": {"type": "array"}
            }
        }
    }
}

class VideoHighlightsDetector:
    """
    Advanced Multi-Feature Video Highlights Detection System

    Implements comprehensive multi-modal analysis for finding the most engaging
    video moments using sophisticated ML models and algorithms.

    Features:
    - spaCy sentence segmentation with timestamp realignment
    - GoEmotions multi-label emotion analysis
    - RoBERTa sentiment analysis with VADER fallback
    - SBERT semantic embeddings for novelty detection
    - Burstiness metrics for engagement analysis
    - Change-point detection for optimal segmentation
    - Max-Marginal-Relevance for diverse highlight selection
    """

    def __init__(self,
                 content_type: str = "general",
                 enable_gpu: bool = True,
                 cache_size: int = 1000):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Core parameters
        self.min_highlight_duration = 10.0  # Minimum 10 seconds
        self.max_highlight_duration = 30.0  # Maximum 30 seconds
        self.overlap_threshold = 1.0        # Minimum gap between highlights
        self.content_type = content_type

        # Feature extraction weights (configurable by content type)
        self.feature_weights = self._get_content_weights(content_type)

        # Max-Marginal-Relevance parameter
        self.mmr_lambda = 0.7  # Balance between relevance and diversity

        # Rolling window for novelty detection (20 seconds)
        self.novelty_window_seconds = 20.0

        # Initialize models (lazy loading)
        self._init_models()

        # Caching system
        self.cache_size = cache_size
        self.embedding_cache = {}
        self.emotion_cache = {}
        self.sentiment_cache = {}

        # Performance tracking
        self.performance_metrics = {
            'processing_times': {},
            'cache_hits': 0,
            'cache_misses': 0
        }

        self.logger.info(f"Advanced Multi-Feature Highlights Detector initialized")
        self.logger.info(f"Content type: {content_type}, GPU enabled: {enable_gpu}")
        self.logger.info(f"Feature weights: {self.feature_weights}")

    def _get_content_weights(self, content_type: str) -> Dict[str, float]:
        """Get feature weights based on content type"""
        weight_configs = {
            "general": {
                "emotion": 0.25,
                "sentiment": 0.20,
                "novelty": 0.20,
                "burstiness": 0.15,
                "semantic": 0.20
            },
            "educational": {
                "emotion": 0.20,
                "sentiment": 0.15,
                "novelty": 0.30,
                "burstiness": 0.10,
                "semantic": 0.25
            },
            "entertainment": {
                "emotion": 0.35,
                "sentiment": 0.25,
                "novelty": 0.15,
                "burstiness": 0.20,
                "semantic": 0.05
            },
            "interview": {
                "emotion": 0.20,
                "sentiment": 0.20,
                "novelty": 0.25,
                "burstiness": 0.15,
                "semantic": 0.20
            }
        }
        return weight_configs.get(content_type, weight_configs["general"])

    def _init_models(self):
        """Initialize ML models with lazy loading"""
        # Set models to None - they will be loaded when first needed
        self.spacy_model = None
        self.emotion_model = None
        self.sentiment_model = None
        self.vader_analyzer = None
        self.embedding_model = None

        # Track initialization status
        self._spacy_initialized = False
        self._emotion_initialized = False
        self._sentiment_initialized = False
        self._vader_initialized = False
        self._embedding_initialized = False

        self.logger.info("Models initialized with lazy loading")

    def find_best_highlights(self, transcript_segments: List[Dict[str, Any]],
                           video_duration: Optional[float] = None,
                           max_highlights: Optional[int] = None,
                           min_duration_override: Optional[float] = None,
                           max_duration_override: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Find optimal highlights using comprehensive multi-feature analysis.

        Args:
            transcript_segments: List of transcript segments with timing
            video_duration: Total video duration (optional)
            max_highlights: Maximum number of highlights to generate
            min_duration_override: Optional override for minimum highlight duration
            max_duration_override: Optional override for maximum highlight duration

        Returns:
            List of optimal highlight segments with comprehensive metadata
        """
        if not transcript_segments:
            return []

        start_time = time.time()

        # Store original durations and apply overrides if provided
        original_min_highlight_duration = self.min_highlight_duration
        original_max_highlight_duration = self.max_highlight_duration

        if min_duration_override is not None:
            self.min_highlight_duration = float(min_duration_override)
            self.logger.info(f"Overriding min_highlight_duration to: {self.min_highlight_duration}s")
        if max_duration_override is not None:
            self.max_highlight_duration = float(max_duration_override)
            self.logger.info(f"Overriding max_highlight_duration to: {self.max_highlight_duration}s")

        self.logger.info(f"Starting multi-feature analysis of {len(transcript_segments)} segments")
        self.logger.info(f"Content type: {self.content_type}, Feature weights: {self.feature_weights}")

        try:
            # Step 1: Text Processing Pipeline - spaCy sentence segmentation
            sentences = self._process_text_pipeline(transcript_segments)
            self.logger.info(f"Processed {len(sentences)} sentences from transcript")

            # Step 2: Feature Extraction for each sentence
            feature_scores = self._extract_features(sentences)
            self.logger.info(f"Extracted features for {len(feature_scores)} sentences")

            # Step 3: Score Fusion - weighted combination of all features
            engagement_scores = self._fuse_scores(feature_scores)
            self.logger.info(f"Computed engagement scores for {len(engagement_scores)} sentences")

            # Step 4: Segmentation Strategy - change-point detection
            segments = self._detect_segments(sentences, engagement_scores)
            self.logger.info(f"Detected {len(segments)} potential highlight segments")

            # Step 5: Highlight Selection - Max-Marginal-Relevance algorithm
            final_highlights = self._select_highlights_mmr(segments, max_highlights)

            # Step 6: Validate and format output
            validated_highlights = self._validate_and_format_output(final_highlights)

            processing_time = time.time() - start_time
            self.performance_metrics['processing_times']['total'] = processing_time

            self.logger.info(f"Selected {len(validated_highlights)} highlights in {processing_time:.2f}s")
            self.logger.info(f"Total duration: {sum(h['duration'] for h in validated_highlights):.1f}s")

            return validated_highlights

        except Exception as e:
            self.logger.error(f"Error in multi-feature analysis: {e}")
            # Fallback to simple pattern-based approach
            return self._fallback_simple_analysis(transcript_segments, max_highlights)

        finally:
            # Restore original durations
            self.min_highlight_duration = original_min_highlight_duration
            self.max_highlight_duration = original_max_highlight_duration

    def _get_spacy_model(self):
        """Lazy load spaCy model"""
        if not self._spacy_initialized:
            self._spacy_initialized = True
            if SPACY_AVAILABLE:
                try:
                    import spacy
                    # Try to load the model, download if not available
                    try:
                        self.spacy_model = spacy.load("en_core_web_sm")
                    except OSError:
                        self.logger.warning("spaCy model 'en_core_web_sm' not found, downloading...")
                        spacy.cli.download("en_core_web_sm")
                        self.spacy_model = spacy.load("en_core_web_sm")

                    self.logger.info("spaCy model loaded successfully")
                except Exception as e:
                    self.logger.error(f"Failed to load spaCy model: {e}")
                    self.spacy_model = None
        return self.spacy_model

    def _get_emotion_model(self):
        """Lazy load GoEmotions model"""
        if not self._emotion_initialized:
            self._emotion_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    from transformers import pipeline
                    self.emotion_model = pipeline(
                        "text-classification",
                        model="SamLowe/roberta-base-go_emotions",
                        return_all_scores=True,
                        device=0 if NUMPY_AVAILABLE else -1  # Use GPU if available
                    )
                    self.logger.info("GoEmotions model loaded successfully")
                except Exception as e:
                    self.logger.error(f"Failed to load GoEmotions model: {e}")
                    self.emotion_model = None
        return self.emotion_model

    def _get_sentiment_model(self):
        """Lazy load RoBERTa sentiment model"""
        if not self._sentiment_initialized:
            self._sentiment_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    from transformers import pipeline
                    self.sentiment_model = pipeline(
                        "sentiment-analysis",
                        model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                        return_all_scores=True,
                        device=0 if NUMPY_AVAILABLE else -1  # Use GPU if available
                    )
                    self.logger.info("RoBERTa sentiment model loaded successfully")
                except Exception as e:
                    self.logger.error(f"Failed to load RoBERTa sentiment model: {e}")
                    self.sentiment_model = None
        return self.sentiment_model

    def _get_vader_analyzer(self):
        """Lazy load VADER sentiment analyzer"""
        if not self._vader_initialized:
            self._vader_initialized = True
            if VADER_AVAILABLE:
                try:
                    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
                    self.vader_analyzer = SentimentIntensityAnalyzer()
                    self.logger.info("VADER sentiment analyzer loaded successfully")
                except Exception as e:
                    self.logger.error(f"Failed to load VADER analyzer: {e}")
                    self.vader_analyzer = None
        return self.vader_analyzer

    def _get_embedding_model(self):
        """Lazy load SBERT embedding model"""
        if not self._embedding_initialized:
            self._embedding_initialized = True
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                try:
                    from sentence_transformers import SentenceTransformer
                    self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
                    self.logger.info("SBERT embedding model loaded successfully")
                except Exception as e:
                    self.logger.error(f"Failed to load SBERT model: {e}")
                    self.embedding_model = None
        return self.embedding_model

    def _process_text_pipeline(self, transcript_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process transcript using spaCy sentence segmentation with timestamp realignment

        Args:
            transcript_segments: List of transcript segments with timing

        Returns:
            List of sentences with realigned timestamps
        """
        spacy_model = self._get_spacy_model()

        if not spacy_model:
            # Fallback to simple sentence splitting
            return self._fallback_sentence_splitting(transcript_segments)

        sentences = []

        for segment in transcript_segments:
            text = segment.get('text', '').strip()
            if not text:
                continue

            start_time = segment.get('start', 0.0)
            end_time = segment.get('end', start_time + 1.0)
            segment_duration = end_time - start_time

            # Process with spaCy
            doc = spacy_model(text)
            spacy_sentences = [sent.text.strip() for sent in doc.sents if sent.text.strip()]

            if not spacy_sentences:
                continue

            # Realign timestamps proportionally
            for i, sentence_text in enumerate(spacy_sentences):
                # Calculate proportional timing
                sentence_start = start_time + (i / len(spacy_sentences)) * segment_duration
                sentence_end = start_time + ((i + 1) / len(spacy_sentences)) * segment_duration

                sentences.append({
                    'text': sentence_text,
                    'start': sentence_start,
                    'end': sentence_end,
                    'duration': sentence_end - sentence_start,
                    'original_segment_idx': len(sentences)
                })

        return sentences

    def _fallback_sentence_splitting(self, transcript_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Fallback sentence splitting when spaCy is not available"""
        sentences = []

        for segment in transcript_segments:
            text = segment.get('text', '').strip()
            if not text:
                continue

            # Simple sentence splitting on periods, exclamation marks, question marks
            import re
            sentence_endings = re.split(r'[.!?]+', text)
            sentence_endings = [s.strip() for s in sentence_endings if s.strip()]

            start_time = segment.get('start', 0.0)
            end_time = segment.get('end', start_time + 1.0)
            segment_duration = end_time - start_time

            for i, sentence_text in enumerate(sentence_endings):
                sentence_start = start_time + (i / len(sentence_endings)) * segment_duration
                sentence_end = start_time + ((i + 1) / len(sentence_endings)) * segment_duration

                sentences.append({
                    'text': sentence_text,
                    'start': sentence_start,
                    'end': sentence_end,
                    'duration': sentence_end - sentence_start,
                    'original_segment_idx': len(sentences)
                })

        return sentences

    def _extract_features(self, sentences: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract comprehensive features for each sentence

        Args:
            sentences: List of sentences with timing information

        Returns:
            List of feature dictionaries for each sentence
        """
        feature_scores = []

        for i, sentence in enumerate(sentences):
            text = sentence['text']

            # Extract all features
            features = {
                'sentence_idx': i,
                'text': text,
                'start': sentence['start'],
                'end': sentence['end'],
                'duration': sentence['duration'],
                'emotion_scores': self._extract_emotion_features(text),
                'sentiment_scores': self._extract_sentiment_features(text),
                'novelty_score': self._extract_novelty_features(text, sentences, i),
                'burstiness_scores': self._extract_burstiness_features(text),
                'semantic_embedding': self._extract_semantic_features(text)
            }

            feature_scores.append(features)

        return feature_scores

    def _extract_emotion_features(self, text: str) -> Dict[str, float]:
        """Extract emotion features using GoEmotions model"""
        # Check cache first
        text_hash = hashlib.md5(text.encode()).hexdigest()
        if text_hash in self.emotion_cache:
            self.performance_metrics['cache_hits'] += 1
            return self.emotion_cache[text_hash]

        self.performance_metrics['cache_misses'] += 1

        emotion_model = self._get_emotion_model()
        if not emotion_model:
            # Fallback to simple emotion detection
            return self._fallback_emotion_detection(text)

        try:
            results = emotion_model(text)

            # Convert to emotion intensity scores
            emotion_scores = {}
            high_engagement_emotions = ['excitement', 'joy', 'surprise', 'anger', 'fear']

            total_score = 0.0
            for result in results:
                emotion = result['label'].lower()
                score = result['score']
                emotion_scores[emotion] = score

                # Weight high-engagement emotions more
                if emotion in high_engagement_emotions:
                    total_score += score * 1.5
                else:
                    total_score += score

            emotion_scores['total_intensity'] = min(1.0, total_score)

            # Cache the result
            if len(self.emotion_cache) < self.cache_size:
                self.emotion_cache[text_hash] = emotion_scores

            return emotion_scores

        except Exception as e:
            self.logger.warning(f"Error in emotion analysis: {e}")
            return self._fallback_emotion_detection(text)

    def _extract_sentiment_features(self, text: str) -> Dict[str, float]:
        """Extract sentiment features using RoBERTa + VADER fallback"""
        # Check cache first
        text_hash = hashlib.md5(text.encode()).hexdigest()
        if text_hash in self.sentiment_cache:
            self.performance_metrics['cache_hits'] += 1
            return self.sentiment_cache[text_hash]

        self.performance_metrics['cache_misses'] += 1

        sentiment_scores = {}

        # Primary: RoBERTa sentiment analysis
        sentiment_model = self._get_sentiment_model()
        if sentiment_model:
            try:
                results = sentiment_model(text)
                for result in results:
                    label = result['label'].lower()
                    score = result['score']
                    sentiment_scores[f'roberta_{label}'] = score

                # Calculate overall sentiment intensity
                positive_score = sentiment_scores.get('roberta_positive', 0.0)
                negative_score = sentiment_scores.get('roberta_negative', 0.0)
                sentiment_scores['roberta_intensity'] = max(positive_score, negative_score)

            except Exception as e:
                self.logger.warning(f"Error in RoBERTa sentiment analysis: {e}")

        # Fallback: VADER sentiment analysis
        vader_analyzer = self._get_vader_analyzer()
        if vader_analyzer:
            try:
                vader_scores = vader_analyzer.polarity_scores(text)
                sentiment_scores['vader_positive'] = vader_scores['pos']
                sentiment_scores['vader_negative'] = vader_scores['neg']
                sentiment_scores['vader_neutral'] = vader_scores['neu']
                sentiment_scores['vader_compound'] = vader_scores['compound']
                sentiment_scores['vader_intensity'] = abs(vader_scores['compound'])

            except Exception as e:
                self.logger.warning(f"Error in VADER sentiment analysis: {e}")

        # Combine scores
        roberta_intensity = sentiment_scores.get('roberta_intensity', 0.0)
        vader_intensity = sentiment_scores.get('vader_intensity', 0.0)
        sentiment_scores['combined_intensity'] = (roberta_intensity + vader_intensity) / 2.0

        # Cache the result
        if len(self.sentiment_cache) < self.cache_size:
            self.sentiment_cache[text_hash] = sentiment_scores

        return sentiment_scores

    def _extract_novelty_features(self, text: str, sentences: List[Dict[str, Any]], current_idx: int) -> float:
        """Extract novelty features using cosine distance to rolling mean embedding"""
        embedding_model = self._get_embedding_model()
        if not embedding_model:
            return 0.5  # Default novelty score

        try:
            # Get current sentence embedding
            current_embedding = embedding_model.encode([text])[0]

            # Get context window (20 seconds before current sentence)
            context_embeddings = []
            current_time = sentences[current_idx]['start']

            for i in range(max(0, current_idx - 10), current_idx):  # Look back up to 10 sentences
                prev_sentence = sentences[i]
                if current_time - prev_sentence['start'] <= self.novelty_window_seconds:
                    prev_embedding = embedding_model.encode([prev_sentence['text']])[0]
                    context_embeddings.append(prev_embedding)

            if not context_embeddings:
                return 0.8  # High novelty if no context

            # Calculate mean context embedding
            if NUMPY_AVAILABLE:
                import numpy as np
                context_mean = np.mean(context_embeddings, axis=0)

                # Calculate cosine distance (1 - cosine similarity)
                from sklearn.metrics.pairwise import cosine_similarity
                similarity = cosine_similarity([current_embedding], [context_mean])[0][0]
                novelty_score = 1.0 - similarity

                return max(0.0, min(1.0, novelty_score))
            else:
                return 0.5  # Default if numpy not available

        except Exception as e:
            self.logger.warning(f"Error in novelty calculation: {e}")
            return 0.5

    def _extract_burstiness_features(self, text: str) -> Dict[str, float]:
        """Extract burstiness metrics for engagement analysis"""
        burstiness_scores = {}

        # Punctuation density analysis
        punctuation_chars = '?!…!!!'
        punctuation_count = sum(text.count(char) for char in punctuation_chars)
        text_length = len(text)
        burstiness_scores['punctuation_density'] = punctuation_count / max(1, text_length) * 100

        # Words per second rate (approximate)
        word_count = len(text.split())
        # Assume average speaking rate for estimation
        estimated_duration = word_count / 3.0  # ~3 words per second average
        burstiness_scores['words_per_second'] = word_count / max(1, estimated_duration)

        # Exclamation intensity
        exclamation_count = text.count('!') + text.count('?')
        burstiness_scores['exclamation_intensity'] = exclamation_count / max(1, word_count)

        # Overall burstiness score
        punctuation_norm = min(1.0, burstiness_scores['punctuation_density'] / 5.0)
        wps_norm = min(1.0, burstiness_scores['words_per_second'] / 5.0)
        exclamation_norm = min(1.0, burstiness_scores['exclamation_intensity'] * 10)

        burstiness_scores['overall_burstiness'] = (punctuation_norm + wps_norm + exclamation_norm) / 3.0

        return burstiness_scores

    def _extract_semantic_features(self, text: str) -> Optional[List[float]]:
        """Extract semantic embeddings using SBERT"""
        # Check cache first
        text_hash = hashlib.md5(text.encode()).hexdigest()
        if text_hash in self.embedding_cache:
            self.performance_metrics['cache_hits'] += 1
            return self.embedding_cache[text_hash]

        self.performance_metrics['cache_misses'] += 1

        embedding_model = self._get_embedding_model()
        if not embedding_model:
            return None

        try:
            embedding = embedding_model.encode([text])[0]
            embedding_list = embedding.tolist() if hasattr(embedding, 'tolist') else list(embedding)

            # Cache the result
            if len(self.embedding_cache) < self.cache_size:
                self.embedding_cache[text_hash] = embedding_list

            return embedding_list

        except Exception as e:
            self.logger.warning(f"Error in semantic embedding: {e}")
            return None

    def _fallback_emotion_detection(self, text: str) -> Dict[str, float]:
        """Fallback emotion detection using simple pattern matching"""
        emotion_scores = {}
        text_lower = text.lower()

        # Simple emotion patterns
        emotion_patterns = {
            'joy': ['happy', 'excited', 'amazing', 'wonderful', 'great', 'fantastic'],
            'anger': ['angry', 'mad', 'furious', 'annoyed', 'frustrated'],
            'fear': ['scared', 'afraid', 'worried', 'nervous', 'anxious'],
            'surprise': ['surprised', 'shocked', 'unexpected', 'wow', 'unbelievable'],
            'sadness': ['sad', 'disappointed', 'depressed', 'upset', 'heartbroken']
        }

        total_intensity = 0.0
        for emotion, patterns in emotion_patterns.items():
            score = sum(1 for pattern in patterns if pattern in text_lower)
            emotion_scores[emotion] = min(1.0, score * 0.2)
            total_intensity += emotion_scores[emotion]

        emotion_scores['total_intensity'] = min(1.0, total_intensity)
        return emotion_scores

    def _fuse_scores(self, feature_scores: List[Dict[str, Any]]) -> List[float]:
        """Fuse all feature scores using weighted linear combination"""
        engagement_scores = []

        for features in feature_scores:
            # Extract individual feature scores
            emotion_score = features['emotion_scores'].get('total_intensity', 0.0)
            sentiment_score = features['sentiment_scores'].get('combined_intensity', 0.0)
            novelty_score = features['novelty_score']
            burstiness_score = features['burstiness_scores'].get('overall_burstiness', 0.0)

            # Semantic score (based on embedding magnitude)
            semantic_embedding = features['semantic_embedding']
            if semantic_embedding and NUMPY_AVAILABLE:
                import numpy as np
                semantic_score = min(1.0, np.linalg.norm(semantic_embedding) / 10.0)
            else:
                semantic_score = 0.5  # Default

            # Weighted combination
            weights = self.feature_weights
            engagement_score = (
                weights['emotion'] * emotion_score +
                weights['sentiment'] * sentiment_score +
                weights['novelty'] * novelty_score +
                weights['burstiness'] * burstiness_score +
                weights['semantic'] * semantic_score
            )

            # Ensure 0-1 range
            engagement_score = max(0.0, min(1.0, engagement_score))
            engagement_scores.append(engagement_score)

        return engagement_scores

    def _detect_segments(self, sentences: List[Dict[str, Any]], engagement_scores: List[float]) -> List[Dict[str, Any]]:
        """Detect segments using change-point detection with adaptive windowing"""
        if len(sentences) < 3:
            # Too few sentences for segmentation
            return self._create_simple_segments(sentences, engagement_scores)

        # Try changeforest first, then ruptures as fallback
        change_points = self._detect_change_points_changeforest(engagement_scores)
        if not change_points:
            change_points = self._detect_change_points_ruptures(engagement_scores)

        if not change_points:
            # Fallback to simple segmentation
            return self._create_simple_segments(sentences, engagement_scores)

        # Create segments from change points
        segments = []
        start_idx = 0

        for cp in change_points:
            if cp > start_idx:
                segment = self._create_segment_from_sentences(
                    sentences, engagement_scores, start_idx, cp
                )
                if segment and self._validate_segment_duration(segment):
                    segments.append(segment)
                start_idx = cp

        # Add final segment
        if start_idx < len(sentences):
            segment = self._create_segment_from_sentences(
                sentences, engagement_scores, start_idx, len(sentences)
            )
            if segment and self._validate_segment_duration(segment):
                segments.append(segment)

        return segments

    def _detect_change_points_changeforest(self, engagement_scores: List[float]) -> List[int]:
        """Detect change points using changeforest library"""
        if not CHANGEFOREST_AVAILABLE or len(engagement_scores) < 5:
            return []

        try:
            import changeforest as cf
            import numpy as np

            # Convert to numpy array
            data = np.array(engagement_scores).reshape(-1, 1)

            # Configure changeforest
            forest = cf.ChangeForest(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                random_state=42
            )

            # Detect change points
            change_points = forest.fit_predict(data)

            # Filter change points to ensure minimum segment duration
            filtered_cps = []
            min_segment_length = 3  # Minimum 3 sentences per segment

            last_cp = 0
            for cp in change_points:
                if cp - last_cp >= min_segment_length:
                    filtered_cps.append(cp)
                    last_cp = cp

            return filtered_cps

        except Exception as e:
            self.logger.warning(f"Error in changeforest detection: {e}")
            return []

    def _detect_change_points_ruptures(self, engagement_scores: List[float]) -> List[int]:
        """Detect change points using ruptures library with PELT algorithm"""
        if not RUPTURES_AVAILABLE or len(engagement_scores) < 5:
            return []

        try:
            import ruptures as rpt
            import numpy as np

            # Convert to numpy array
            signal = np.array(engagement_scores)

            # Use PELT algorithm for change point detection
            algo = rpt.Pelt(model="rbf", min_size=3, jump=1)
            change_points = algo.fit_predict(signal, pen=1.0)

            # Remove the last change point (end of signal)
            if change_points and change_points[-1] == len(engagement_scores):
                change_points = change_points[:-1]

            return change_points

        except Exception as e:
            self.logger.warning(f"Error in ruptures detection: {e}")
            return []

    def _create_simple_segments(self, sentences: List[Dict[str, Any]], engagement_scores: List[float]) -> List[Dict[str, Any]]:
        """Create simple segments when change-point detection fails"""
        segments = []

        # Create segments of approximately 15-20 seconds each
        target_duration = 15.0
        current_start = 0
        current_duration = 0.0

        for i, sentence in enumerate(sentences):
            current_duration += sentence['duration']

            # Create segment when target duration is reached or at end
            if current_duration >= target_duration or i == len(sentences) - 1:
                segment = self._create_segment_from_sentences(
                    sentences, engagement_scores, current_start, i + 1
                )
                if segment and self._validate_segment_duration(segment):
                    segments.append(segment)

                current_start = i + 1
                current_duration = 0.0

        return segments

    def _create_segment_from_sentences(self, sentences: List[Dict[str, Any]],
                                     engagement_scores: List[float],
                                     start_idx: int, end_idx: int) -> Optional[Dict[str, Any]]:
        """Create a segment from a range of sentences"""
        if start_idx >= end_idx or start_idx >= len(sentences):
            return None

        end_idx = min(end_idx, len(sentences))
        segment_sentences = sentences[start_idx:end_idx]
        segment_scores = engagement_scores[start_idx:end_idx]

        if not segment_sentences:
            return None

        # Calculate segment properties
        start_time = segment_sentences[0]['start']
        end_time = segment_sentences[-1]['end']
        duration = end_time - start_time

        # Combine text
        text_parts = [s['text'] for s in segment_sentences]
        combined_text = ' '.join(text_parts)

        # Calculate average engagement score
        avg_engagement = sum(segment_scores) / len(segment_scores) if segment_scores else 0.0

        # Find peak engagement index within segment
        if segment_scores:
            peak_idx = segment_scores.index(max(segment_scores))
            peak_engagement_index = start_idx + peak_idx
        else:
            peak_engagement_index = start_idx

        return {
            'start_time': start_time,
            'end_time': end_time,
            'duration': duration,
            'text': combined_text,
            'engagement_score': avg_engagement,
            'sentence_indices': list(range(start_idx, end_idx)),
            'peak_engagement_index': peak_engagement_index,
            'sentence_count': len(segment_sentences)
        }

    def _validate_segment_duration(self, segment: Dict[str, Any]) -> bool:
        """Validate segment duration constraints"""
        duration = segment.get('duration', 0.0)
        return self.min_highlight_duration <= duration <= self.max_highlight_duration

    def _select_highlights_mmr(self, segments: List[Dict[str, Any]], max_highlights: Optional[int]) -> List[Dict[str, Any]]:
        """Select highlights using Max-Marginal-Relevance algorithm"""
        if not segments:
            return []

        max_highlights = max_highlights or 10
        selected_highlights = []
        remaining_segments = segments.copy()

        # Sort by engagement score initially
        remaining_segments.sort(key=lambda x: x['engagement_score'], reverse=True)

        # Select first highlight (highest engagement)
        if remaining_segments:
            selected_highlights.append(remaining_segments.pop(0))

        # Select remaining highlights using MMR
        while len(selected_highlights) < max_highlights and remaining_segments:
            best_segment = None
            best_mmr_score = -1.0
            best_idx = -1

            for i, candidate in enumerate(remaining_segments):
                # Calculate relevance score (engagement)
                relevance = candidate['engagement_score']

                # Calculate diversity score (minimum similarity to selected)
                diversity = self._calculate_diversity_score(candidate, selected_highlights)

                # MMR score: λ * relevance + (1-λ) * diversity
                mmr_score = self.mmr_lambda * relevance + (1 - self.mmr_lambda) * diversity

                if mmr_score > best_mmr_score:
                    best_mmr_score = mmr_score
                    best_segment = candidate
                    best_idx = i

            if best_segment:
                selected_highlights.append(best_segment)
                remaining_segments.pop(best_idx)
            else:
                break

        # Sort selected highlights by start time
        selected_highlights.sort(key=lambda x: x['start_time'])

        return selected_highlights

    def _calculate_diversity_score(self, candidate: Dict[str, Any], selected_highlights: List[Dict[str, Any]]) -> float:
        """Calculate diversity score for MMR algorithm"""
        if not selected_highlights:
            return 1.0

        candidate_text = candidate['text']
        min_similarity = 1.0

        # Calculate text similarity with all selected highlights
        for selected in selected_highlights:
            selected_text = selected['text']
            similarity = self._calculate_text_similarity(candidate_text, selected_text)
            min_similarity = min(min_similarity, similarity)

        # Diversity is 1 - similarity
        return 1.0 - min_similarity

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate text similarity using simple word overlap"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _validate_and_format_output(self, highlights: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and format final highlight output with comprehensive metadata"""
        validated_highlights = []

        for i, highlight in enumerate(highlights):
            # Validate against JSON schema
            if not self._validate_highlight_schema(highlight):
                self.logger.warning(f"Highlight {i} failed schema validation, skipping")
                continue

            # Validate duration requirements
            duration = highlight.get('duration', 0.0)
            if HighlightValidationUtils:
                if not HighlightValidationUtils.validate_highlight_duration(duration):
                    self.logger.debug(f"Skipping highlight with duration {duration:.1f}s (below minimum)")
                    continue
            else:
                if duration < MIN_HIGHLIGHT_DURATION_SECONDS:
                    self.logger.debug(f"Skipping highlight with duration {duration:.1f}s (below minimum)")
                    continue

            # Format with comprehensive metadata
            formatted_highlight = {
                'start_time': highlight['start_time'],
                'end_time': highlight['end_time'],
                'duration': highlight['duration'],
                'engagement_score': highlight['engagement_score'],
                'text': highlight.get('text', ''),
                'algorithm': 'multi_feature_analysis',
                'metadata': {
                    'feature_scores': {
                        'emotion_intensity': highlight.get('emotion_score', 0.0),
                        'sentiment_intensity': highlight.get('sentiment_score', 0.0),
                        'novelty_score': highlight.get('novelty_score', 0.0),
                        'burstiness_score': highlight.get('burstiness_score', 0.0),
                        'semantic_score': highlight.get('semantic_score', 0.0)
                    },
                    'peak_engagement_index': highlight.get('peak_engagement_index', 0),
                    'confidence_score': min(1.0, highlight['engagement_score'] * 1.2),
                    'segment_boundaries': {
                        'sentence_indices': highlight.get('sentence_indices', []),
                        'sentence_count': highlight.get('sentence_count', 0)
                    },
                    'processing_info': {
                        'content_type': self.content_type,
                        'feature_weights': self.feature_weights,
                        'mmr_lambda': self.mmr_lambda
                    }
                }
            }

            validated_highlights.append(formatted_highlight)

        return validated_highlights

    def _validate_highlight_schema(self, highlight: Dict[str, Any]) -> bool:
        """Validate highlight against JSON schema"""
        try:
            # Basic validation
            required_fields = ['start_time', 'end_time', 'duration', 'engagement_score']
            for field in required_fields:
                if field not in highlight:
                    return False

            # Type validation
            if not isinstance(highlight['start_time'], (int, float)):
                return False
            if not isinstance(highlight['end_time'], (int, float)):
                return False
            if not isinstance(highlight['duration'], (int, float)):
                return False
            if not isinstance(highlight['engagement_score'], (int, float)):
                return False

            # Range validation
            if highlight['start_time'] < 0:
                return False
            if highlight['end_time'] <= highlight['start_time']:
                return False
            if not (10 <= highlight['duration'] <= 30):
                return False
            if not (0 <= highlight['engagement_score'] <= 1):
                return False

            return True

        except Exception as e:
            self.logger.warning(f"Error in schema validation: {e}")
            return False

    def _fallback_simple_analysis(self, transcript_segments: List[Dict[str, Any]],
                                 max_highlights: Optional[int]) -> List[Dict[str, Any]]:
        """Fallback to simple pattern-based analysis when ML models fail"""
        self.logger.info("Using fallback simple analysis")

        # Use the existing pattern-based approach as fallback
        segment_scores = self._analyze_segment_engagement(transcript_segments)
        momentum_peaks = self._find_momentum_peaks(transcript_segments, segment_scores)
        narrative_peaks = self._find_narrative_peaks(transcript_segments)

        candidates = self._generate_highlight_candidates(
            transcript_segments, segment_scores, momentum_peaks, narrative_peaks
        )

        scored_candidates = self._score_highlight_candidates(candidates, transcript_segments)
        final_highlights = self._select_optimal_highlights(scored_candidates, max_highlights)

        return final_highlights

    def _analyze_segment_engagement(self, transcript_segments: List[Dict[str, Any]]) -> Dict[int, float]:
        """
        Analyze each segment for engagement potential using simple pattern matching.
        This is used as a fallback when ML models are not available.

        Args:
            transcript_segments: List of transcript segments

        Returns:
            Dictionary mapping segment index to engagement score (0.0-1.0)
        """
        segment_scores = {}

        # Simple engagement patterns for fallback
        engagement_patterns = {
            'curiosity_triggers': [
                'what if', 'imagine', 'picture this', 'the secret', 'the truth',
                'nobody tells you', 'most people don\'t', 'the real reason'
            ],
            'emotional_peaks': [
                'incredible', 'amazing', 'unbelievable', 'shocking', 'surprising'
            ],
            'authority_signals': [
                'research shows', 'studies prove', 'data reveals', 'experts say'
            ]
        }

        energy_indicators = {
            'high_energy': ['!', 'really', 'absolutely', 'definitely'],
            'emphasis': ['very', 'extremely', 'incredibly']
        }

        for i, segment in enumerate(transcript_segments):
            text = segment.get('text', '').lower()
            score = 0.0

            # Check for engagement patterns
            for pattern_type, patterns in engagement_patterns.items():
                for pattern in patterns:
                    if pattern in text:
                        if pattern_type == 'curiosity_triggers':
                            score += 0.3
                        elif pattern_type == 'emotional_peaks':
                            score += 0.25
                        elif pattern_type == 'authority_signals':
                            score += 0.2

            # Check for energy indicators
            for energy_type, indicators in energy_indicators.items():
                for indicator in indicators:
                    if indicator in text:
                        if energy_type == 'high_energy':
                            score += 0.1
                        elif energy_type == 'emphasis':
                            score += 0.05

            # Normalize score to 0-1 range
            segment_scores[i] = min(1.0, score)

        return segment_scores

    def _find_momentum_peaks(self, transcript_segments: List[Dict[str, Any]],
                           segment_scores: Dict[int, float]) -> List[int]:
        """
        Find segments with high information density and momentum.

        Args:
            transcript_segments: List of transcript segments
            segment_scores: Engagement scores for each segment

        Returns:
            List of segment indices representing momentum peaks
        """
        if len(segment_scores) < 3:
            return list(segment_scores.keys())

        # Calculate moving average of scores
        window_size = 3
        momentum_peaks = []

        for i in range(len(transcript_segments)):
            if i < window_size - 1:
                continue

            # Calculate average score in window
            window_scores = [segment_scores.get(j, 0.0) for j in range(i - window_size + 1, i + 1)]
            avg_score = sum(window_scores) / len(window_scores)

            # Check if this is a local peak
            if avg_score > 0.3:  # Threshold for momentum
                momentum_peaks.append(i)

        return momentum_peaks

    def _find_narrative_peaks(self, transcript_segments: List[Dict[str, Any]]) -> List[int]:
        """
        Find segments that represent narrative or emotional peaks.

        Args:
            transcript_segments: List of transcript segments

        Returns:
            List of segment indices representing narrative peaks
        """
        narrative_peaks = []

        for i, segment in enumerate(transcript_segments):
            text = segment.get('text', '').lower()

            # Look for narrative markers
            narrative_markers = [
                'that\'s when', 'suddenly', 'then i realized', 'the moment',
                'breakthrough', 'turning point', 'game changer', 'life changing',
                'incredible', 'amazing', 'unbelievable', 'shocking'
            ]

            for marker in narrative_markers:
                if marker in text:
                    narrative_peaks.append(i)
                    break

        return narrative_peaks

    def _generate_highlight_candidates(self, transcript_segments: List[Dict[str, Any]],
                                     segment_scores: Dict[int, float],
                                     momentum_peaks: List[int],
                                     narrative_peaks: List[int]) -> List[Dict[str, Any]]:
        """
        Generate candidate highlight windows based on peaks and scores.

        Args:
            transcript_segments: List of transcript segments
            segment_scores: Engagement scores for each segment
            momentum_peaks: List of momentum peak indices
            narrative_peaks: List of narrative peak indices

        Returns:
            List of candidate highlight dictionaries
        """
        candidates = []
        all_peaks = set(momentum_peaks + narrative_peaks)

        for peak_idx in all_peaks:
            if peak_idx >= len(transcript_segments):
                continue

            # Generate windows of different sizes around each peak
            for window_size in [3, 5, 7, 10]:  # Number of segments
                start_idx = max(0, peak_idx - window_size // 2)
                end_idx = min(len(transcript_segments) - 1, peak_idx + window_size // 2)

                if end_idx <= start_idx:
                    continue

                # Calculate timing
                start_time = transcript_segments[start_idx].get('start', 0.0)
                end_time = transcript_segments[end_idx].get('end', start_time + 10.0)
                duration = end_time - start_time

                # Check duration constraints
                if duration < self.min_highlight_duration or duration > self.max_highlight_duration:
                    continue

                # Combine text from segments
                text_parts = []
                for i in range(start_idx, end_idx + 1):
                    text_parts.append(transcript_segments[i].get('text', ''))
                combined_text = ' '.join(text_parts)

                candidate = {
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'start_segment_idx': start_idx,
                    'end_segment_idx': end_idx,
                    'peak_idx': peak_idx,
                    'text': combined_text,
                    'window_size': window_size
                }

                candidates.append(candidate)

        return candidates

    def _score_highlight_candidates(self, candidates: List[Dict[str, Any]],
                                  transcript_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Score and rank highlight candidates.

        Args:
            candidates: List of candidate highlight dictionaries
            transcript_segments: List of transcript segments

        Returns:
            List of scored candidates sorted by score (highest first)
        """
        scored_candidates = []

        for candidate in candidates:
            score = self._calculate_candidate_score(candidate, transcript_segments)
            candidate['engagement_score'] = score
            scored_candidates.append(candidate)

        # Sort by score (highest first)
        scored_candidates.sort(key=lambda x: x['engagement_score'], reverse=True)

        return scored_candidates

    def _calculate_candidate_score(self, candidate: Dict[str, Any],
                                 transcript_segments: List[Dict[str, Any]]) -> float:
        """
        Calculate engagement score for a candidate highlight using simple patterns.

        Args:
            candidate: Candidate highlight dictionary
            transcript_segments: List of transcript segments (unused in this implementation)

        Returns:
            Engagement score (0.0-1.0)
        """
        text = candidate['text'].lower()
        score = 0.0

        # Simple engagement patterns for fallback scoring
        engagement_patterns = {
            'curiosity_triggers': ['what if', 'imagine', 'the secret', 'the truth'],
            'emotional_peaks': ['incredible', 'amazing', 'unbelievable', 'shocking'],
            'authority_signals': ['research shows', 'studies prove', 'experts say']
        }

        energy_indicators = {
            'high_energy': ['!', 'really', 'absolutely'],
            'emphasis': ['very', 'extremely', 'incredibly']
        }

        # Base engagement from pattern matching
        for pattern_type, patterns in engagement_patterns.items():
            pattern_count = sum(1 for pattern in patterns if pattern in text)
            if pattern_count > 0:
                if pattern_type == 'curiosity_triggers':
                    score += min(0.4, pattern_count * 0.1)
                elif pattern_type == 'emotional_peaks':
                    score += min(0.3, pattern_count * 0.1)
                elif pattern_type == 'authority_signals':
                    score += min(0.2, pattern_count * 0.05)

        # Energy indicators
        for energy_type, indicators in energy_indicators.items():
            energy_count = sum(1 for indicator in indicators if indicator in text)
            if energy_count > 0:
                score += min(0.1, energy_count * 0.02)

        # Duration bonus (prefer optimal length)
        duration = candidate['duration']
        if 15.0 <= duration <= 25.0:  # Sweet spot
            score += 0.1
        elif 10.0 <= duration <= 30.0:  # Acceptable range
            score += 0.05

        # Text length bonus (prefer substantial content)
        word_count = len(text.split())
        if word_count >= 50:
            score += 0.1
        elif word_count >= 30:
            score += 0.05

        return min(1.0, score)

    def _select_optimal_highlights(self, scored_candidates: List[Dict[str, Any]],
                                 max_highlights: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Select the optimal set of non-overlapping highlights.

        Args:
            scored_candidates: List of scored candidate highlights
            max_highlights: Maximum number of highlights to select

        Returns:
            List of selected optimal highlights
        """
        if not scored_candidates:
            return []

        # Set default max_highlights if not provided
        if max_highlights is None:
            max_highlights = 100  # Default for production

        selected_highlights = []
        used_time_ranges = []

        for candidate in scored_candidates:
            if len(selected_highlights) >= max_highlights:
                break

            start_time = candidate['start_time']
            end_time = candidate['end_time']

            # Check for overlap with already selected highlights
            has_overlap = False
            for used_start, used_end in used_time_ranges:
                # Check if there's significant overlap (more than overlap_threshold)
                overlap_start = max(start_time, used_start)
                overlap_end = min(end_time, used_end)
                overlap_duration = max(0, overlap_end - overlap_start)

                if overlap_duration > self.overlap_threshold:
                    has_overlap = True
                    break

            if not has_overlap:
                # Apply minimum duration validation if available
                duration = candidate['duration']
                if HighlightValidationUtils:
                    if not HighlightValidationUtils.validate_highlight_duration(duration):
                        self.logger.debug(f"Skipping highlight with duration {duration:.1f}s (below minimum)")
                        continue
                else:
                    # Fallback validation
                    if duration < MIN_HIGHLIGHT_DURATION_SECONDS:
                        self.logger.debug(f"Skipping highlight with duration {duration:.1f}s (below minimum)")
                        continue

                # Create final highlight format
                highlight = {
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'engagement_score': candidate['engagement_score'],
                    'text': candidate.get('text', ''),
                    'start_segment_idx': candidate.get('start_segment_idx'),
                    'end_segment_idx': candidate.get('end_segment_idx'),
                    'algorithm': 'advanced_content_analysis',
                    'metadata': {
                        'peak_idx': candidate.get('peak_idx'),
                        'window_size': candidate.get('window_size'),
                        'score_breakdown': {
                            'engagement': candidate['engagement_score']
                        }
                    }
                }

                selected_highlights.append(highlight)
                used_time_ranges.append((start_time, end_time))

        # Sort final highlights by start time
        selected_highlights.sort(key=lambda x: x['start_time'])

        # Log selection summary
        total_duration = sum(h['duration'] for h in selected_highlights)
        avg_score = sum(h['engagement_score'] for h in selected_highlights) / len(selected_highlights) if selected_highlights else 0

        self.logger.info(f"Selected {len(selected_highlights)} highlights:")
        self.logger.info(f"  Total duration: {total_duration:.1f}s")
        self.logger.info(f"  Average engagement score: {avg_score:.3f}")

        return selected_highlights
